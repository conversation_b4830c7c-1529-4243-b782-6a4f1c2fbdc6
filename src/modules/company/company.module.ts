import { Modu<PERSON> } from '@nestjs/common';
import { CompanyController } from './company.controller';
import { CreateCompanyUseCase } from '../../core/application/use-cases/company/create-company.use-case';
import { UpdateCompanyUseCase } from '../../core/application/use-cases/company/update-company.use-case';
import { DeleteCompanyUseCase } from '../../core/application/use-cases/company/delete-company.use-case';
import { PrismaCompanyRepository } from '../../infrastructure/repositories/prisma-company.repository';
import { PrismaService } from '../../infrastructure/prisma/prisma.service';
import { COMPANY_REPOSITORY } from '../../core/constants/injection-tokens';
import { CompanyService } from '../finance/services/company.service';

@Module({
  controllers: [CompanyController],
  providers: [
    CreateCompanyUseCase,
    UpdateCompanyUseCase,
    DeleteCompanyUseCase,
    PrismaService,
    CompanyService,
    {
      provide: COMPANY_REPOSITORY,
      useClass: PrismaCompanyRepository,
    },
  ],
})
export class CompanyModule {}
