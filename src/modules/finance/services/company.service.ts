import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../../infrastructure/prisma/prisma.service';
import { ListCompaniesDto } from '../dto/list-companies.dto';
import { PaginatedResponseDto } from '../dto/paginated-response.dto';
import { Company, Prisma } from '@prisma/client';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class CompanyService {
  constructor(private readonly prisma: PrismaService) {}

  async getCompanyByUuid(uuid: string): Promise<Company> {
    const company = await (
      this.prisma as unknown as PrismaClient
    ).company.findUnique({
      where: { uuid },
    });

    if (!company) {
      throw new NotFoundException('Empresa não encontrada');
    }

    return company;
  }

  async listCompanies(
    filters: ListCompaniesDto,
  ): Promise<PaginatedResponseDto<Company>> {
    const where: Prisma.CompanyWhereInput = {
      ...(filters.cnpj && { cnpj: filters.cnpj }),
      ...(filters.razaoSocial && {
        razaoSocial: { contains: filters.razaoSocial, mode: 'insensitive' },
      }),
    };

    const [items, total] = await Promise.all([
      (this.prisma as unknown as PrismaClient).company.findMany({
        where,
        skip: filters.offset ?? 0,
        take: filters.limit ?? 20,
        orderBy: { razaoSocial: 'asc' },
      }),
      (this.prisma as unknown as PrismaClient).company.count({ where }),
    ]);

    return new PaginatedResponseDto<Company>(
      items,
      total,
      filters.limit ?? 20,
      filters.offset ?? 0,
    );
  }
}
