import { ApiProperty } from '@nestjs/swagger';
import { EmployeeStatus } from '@/core/domain/enums/employee.enum';
import { WorkSchedule, Vacation } from '@/core/domain/entities/employee.entity';
import {
  Shift,
  ContractType,
  Seniority,
} from '@/core/domain/enums/employee.enum';

export class EmployeeResponseDto {
  @ApiProperty({
    description: 'Identificador único (UUID) do colaborador',
    example: '123e4567-e89b-12d3-a456-************',
  })
  uuid: string;

  @ApiProperty({
    description: 'Employee name',
    example: '<PERSON>',
  })
  name: string;

  @ApiProperty({
    description: 'Employee email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Cargo do colaborador',
    example: 'Desenvolvedor Senior',
  })
  position: string;

  @ApiProperty({
    description: 'Departamento do colaborador',
    example: 'Tecnologia',
  })
  department: string;

  @ApiProperty({
    description: 'Data de contratação do colaborador',
    example: '2024-01-01',
  })
  hireDate: string;

  @ApiProperty({
    description: 'Endereço do colaborador',
    example: {
      street: 'Rua das Flores',
      number: '123',
      complement: 'Apto 101',
      neighborhood: 'Centro',
      city: 'São Paulo',
      state: 'SP',
      zipCode: '01234-567',
    },
  })
  address: {
    street: string;
    number: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };

  @ApiProperty({
    description: 'Documentos pessoais',
    example: [
      {
        type: 'CPF',
        number: '123.456.789-00',
        issuingAgency: null,
        issueDate: null,
      },
    ],
  })
  personalDocuments: Array<{
    type: string;
    number: string;
    issuingAgency?: string;
    issueDate?: string;
  }>;

  @ApiProperty({
    description: 'Dependentes',
    example: [
      {
        name: 'Maria Silva',
        kinship: 'Filho(a)',
        birthDate: '2015-05-10',
        isTaxDependent: true,
        hasHealthPlan: true,
      },
    ],
  })
  dependents: Array<{
    name: string;
    kinship: string;
    birthDate?: string;
    isTaxDependent?: boolean;
    hasHealthPlan?: boolean;
  }>;

  @ApiProperty({
    description: 'Status do colaborador',
    enum: EmployeeStatus,
    example: EmployeeStatus.ACTIVE,
  })
  status: EmployeeStatus;

  @ApiProperty({
    description: 'Escala de trabalho',
    example: {
      monday: '08:00-17:00',
      tuesday: '08:00-17:00',
      wednesday: '08:00-17:00',
      thursday: '08:00-17:00',
      friday: '08:00-17:00',
      saturday: 'OFF',
      sunday: 'OFF',
    },
  })
  workSchedule?: WorkSchedule;

  @ApiProperty({
    description: 'Turno de trabalho',
    enum: Shift,
    example: Shift.MORNING,
  })
  shift?: Shift;

  @ApiProperty({
    description: 'Salário bruto',
    example: 5000.0,
  })
  grossSalary?: number;

  @ApiProperty({
    description: 'Vale refeição',
    example: 500.0,
  })
  mealAllowance?: number;

  @ApiProperty({
    description: 'Vale transporte',
    example: 300.0,
  })
  transportAllowance?: number;

  @ApiProperty({
    description: 'Plano de saúde',
    example: 'Unimed',
  })
  healthPlan?: string;

  @ApiProperty({
    description: 'Tipo de contrato',
    enum: ContractType,
    example: ContractType.PJ,
  })
  contractType?: ContractType;

  @ApiProperty({
    description: 'Senioridade',
    enum: Seniority,
    example: Seniority.JUNIOR,
  })
  seniority?: Seniority;

  @ApiProperty({
    description: 'Telefone',
    example: '***********',
  })
  phone?: string;

  @ApiProperty({
    description: 'Data de nascimento',
    example: '1990-01-01',
  })
  birthDate?: string;

  @ApiProperty({
    description: 'Horário de trabalho',
    example: '08:00-17:00',
  })
  workHours?: string;

  @ApiProperty({
    description: 'Banco de horas',
    example: true,
  })
  overtimeBank?: boolean;

  @ApiProperty({
    description: 'Férias',
    example: [
      {
        startDate: '2024-07-01',
        endDate: '2024-07-30',
      },
    ],
  })
  vacations?: Vacation[];

  @ApiProperty({
    description: 'Jornada de trabalho calculada com base na escala',
    example: '40 horas semanais',
  })
  workJourney?: string;

  @ApiProperty({
    description: 'UUID do usuário que criou o colaborador',
    example: 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6',
  })
  createdBy: string;

  @ApiProperty({
    description: 'UUID do usuário que atualizou o colaborador',
    example: 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6',
  })
  updatedBy: string;

  @ApiProperty({
    description: 'Timestamp da criação em formato ISO',
    example: '2023-01-15T10:30:00Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Timestamp da última atualização em formato ISO',
    example: '2023-02-20T14:45:30Z',
  })
  updatedAt: string;

  @ApiProperty({
    description: 'ID do usuário associado',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  userId?: string;
}
