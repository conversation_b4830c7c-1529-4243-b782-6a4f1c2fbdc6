import { Test, TestingModule } from '@nestjs/testing';
import { PayablesTypeService } from '../../services/payables-type.service';
import { CreatePayablesTypeUseCase } from '@core/application/use-cases/payables-type/create-payables-type.use-case';
import { ListPayablesTypeUseCase } from '@core/application/use-cases/payables-type/list-payables-type.use-case';
import { ListPayablesTypeByUuidUseCase } from '@core/application/use-cases/payables-type/list-payable-type-by-uuid.use-case';
import { UpdatePayablesTypeUseCase } from '@core/application/use-cases/payables-type/update-payable-type.use-case';
import { DeletePayableTypeUseCase } from '@core/application/use-cases/payables-type/delete-payable-type.use-case';
import { CreatePayablesTypeDto } from '@modules/finance/dto/create-payables-type.dto';
import { UpdatePayablesTypeDto } from '@modules/finance/dto/update-payables-type.dto';
import { PayablesType } from '@core/domain/payables-type/entities/payables-type.entity';

describe('PayablesTypeService', () => {
  let service: PayablesTypeService;
  let createPayablesTypeUseCase: CreatePayablesTypeUseCase;
  let listPayablesTypeUseCase: ListPayablesTypeUseCase;
  let listPayablesTypeByUuidUseCase: ListPayablesTypeByUuidUseCase;
  let updatePayablesTypeUseCase: UpdatePayablesTypeUseCase;
  let deletePayableTypeUseCase: DeletePayableTypeUseCase;

  // Mock data for the PayablesType entity
  const mockPayablesTypeData = {
    id: 'payables-type-uuid',
    code: 'PT001',
    description: 'Test Payables Type',
    createdBy: 'user-uuid',
    createdAt: '2023-01-01T00:00:00.000Z',
    updatedBy: 'user-uuid',
    updatedAt: '2023-01-01T00:00:00.000Z',
  };

  // Mock the PayablesType entity
  const mockPayablesType = {
    _id: 'payables-type-uuid',
    _code: 'PT001',
    _description: 'Test Payables Type',
    _createdBy: 'user-uuid',
    _createdAt: new Date('2023-01-01'),
    _updatedBy: 'user-uuid',
    _updatedAt: new Date('2023-01-01'),
    validate: jest.fn(),
    id: 'payables-type-uuid',
    code: 'PT001',
    description: 'Test Payables Type',
    createdBy: 'user-uuid',
    createdAt: new Date('2023-01-01'),
    updatedBy: 'user-uuid',
    updatedAt: new Date('2023-01-01'),
    toJSON: jest.fn().mockReturnValue(mockPayablesTypeData),
  } as unknown as PayablesType;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PayablesTypeService,
        {
          provide: CreatePayablesTypeUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: ListPayablesTypeUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: ListPayablesTypeByUuidUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: UpdatePayablesTypeUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: DeletePayableTypeUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<PayablesTypeService>(PayablesTypeService);
    createPayablesTypeUseCase = module.get<CreatePayablesTypeUseCase>(
      CreatePayablesTypeUseCase,
    );
    listPayablesTypeUseCase = module.get<ListPayablesTypeUseCase>(
      ListPayablesTypeUseCase,
    );
    listPayablesTypeByUuidUseCase = module.get<ListPayablesTypeByUuidUseCase>(
      ListPayablesTypeByUuidUseCase,
    );
    updatePayablesTypeUseCase = module.get<UpdatePayablesTypeUseCase>(
      UpdatePayablesTypeUseCase,
    );
    deletePayableTypeUseCase = module.get<DeletePayableTypeUseCase>(
      DeletePayableTypeUseCase,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a payables type and return it as DTO', async () => {
      const createDto: CreatePayablesTypeDto = {
        code: 'PT001',
        description: 'Test Payables Type',
        createdBy: 'user-uuid',
        updatedBy: 'user-uuid',
      };

      const executeSpy = jest
        .spyOn(createPayablesTypeUseCase, 'execute')
        .mockResolvedValue(mockPayablesType);

      const result = await service.create(createDto);

      expect(executeSpy).toHaveBeenCalledWith(createDto);
      expect(result).toEqual(mockPayablesTypeData);
    });
  });

  describe('findByUuid', () => {
    it('should return a payables type by uuid', async () => {
      const uuid = 'payables-type-uuid';

      const executeSpy = jest
        .spyOn(listPayablesTypeByUuidUseCase, 'execute')
        .mockResolvedValue(mockPayablesType);

      const result = await service.findByUuid(uuid);

      expect(executeSpy).toHaveBeenCalledWith(uuid);
      expect(result).toEqual(mockPayablesTypeData);
    });
  });

  describe('listPayablesTypes', () => {
    it('should return a paginated list of payables types', async () => {
      const query = { limit: 10, offset: 0, description: 'Test' };
      const mockResult = {
        items: [mockPayablesType],
        total: 1,
      };

      const executeSpy = jest
        .spyOn(listPayablesTypeUseCase, 'execute')
        .mockResolvedValue(mockResult);

      const result = await service.listPayablesTypes(query);

      expect(executeSpy).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        description: 'Test',
      });
      expect(result).toEqual({
        items: [mockPayablesTypeData],
        total: 1,
        limit: 10,
        offset: 0,
      });
    });
  });

  describe('update', () => {
    it('should update a payables type and return it as DTO', async () => {
      const uuid = 'payables-type-uuid';
      const updateDto: UpdatePayablesTypeDto = {
        description: 'Updated Payables Type',
        updatedBy: 'user-uuid',
      };

      // Updated mock data
      const updatedMockData = {
        ...mockPayablesTypeData,
        description: 'Updated Payables Type',
        updatedAt: '2023-01-02T00:00:00.000Z',
      };

      // Create an updated mock payables type
      const updatedMockPayablesType = {
        ...mockPayablesType,
        _description: 'Updated Payables Type',
        description: 'Updated Payables Type',
        _updatedAt: new Date('2023-01-02'),
        updatedAt: new Date('2023-01-02'),
        toJSON: jest.fn().mockReturnValue(updatedMockData),
      } as unknown as PayablesType;

      const executeSpy = jest
        .spyOn(updatePayablesTypeUseCase, 'execute')
        .mockResolvedValue(updatedMockPayablesType);

      const result = await service.update(uuid, updateDto);

      expect(executeSpy).toHaveBeenCalledWith(uuid, updateDto);
      expect(result).toEqual(updatedMockData);
    });
  });

  describe('delete', () => {
    it('should delete a payables type', async () => {
      const uuid = 'payables-type-uuid';

      const executeSpy = jest
        .spyOn(deletePayableTypeUseCase, 'execute')
        .mockResolvedValue(undefined);

      await service.delete(uuid);

      expect(executeSpy).toHaveBeenCalledWith({ uuid });
    });
  });
});
