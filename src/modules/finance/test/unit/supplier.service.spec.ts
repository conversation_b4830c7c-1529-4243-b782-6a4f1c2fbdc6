import { Test, TestingModule } from '@nestjs/testing';
import { SupplierService } from '../../services/supplier.service';
import { CreateSupplierUseCase } from '@/core/application/use-cases/supplier/create-supplier.use-case';
import { ListSupplierByUuidUseCase } from '@/core/application/use-cases/supplier/list-supplier-by-uuid.use-case';
import { ListSuppliersUseCase } from '@/core/application/use-cases/supplier/list-suppliers.use-case';
import { DeleteSupplierUseCase } from '@/core/application/use-cases/supplier/delete-supplier.use-case';
import { UpdateSupplierUseCase } from '@/core/application/use-cases/supplier/update-supplier.use-case';
import { CreateSupplierDto } from '../../dto/create-supplier.dto';
import { UpdateSupplierDto } from '../../dto/update-supplier.dto';
import { Supplier } from '@/core/domain/supplier/entities/supplier.entity';
import { SupplierStatus } from '@/core/domain/supplier/enums/supplier-status.enum';
import { SupplierType } from '@/core/domain/supplier/enums/supplier-type.enum';
import { CreateDocumentUseCase } from '../../../documents/application/use-cases/create-document.use-case';
import { ListDocumentsUseCase } from '../../../documents/application/use-cases/list-documents.use-case';
import { ListSupplierContactsUseCase } from '@/core/application/use-cases/supplier/list-supplier-contacts.use-case';
import { CreateSupplierContactUseCase } from '@/core/application/use-cases/supplier/create-supplier-contact.use-case';
import { UpdateSupplierContactUseCase } from '@/core/application/use-cases/supplier/update-supplier-contact.usecase';
import { DeleteSupplierContactUseCase } from '@/core/application/use-cases/supplier/delete-supplier-contact.usecase';
import { GetSupplierByUserIdUsecase } from '@/core/application/use-cases/supplier/get-supplier-by-user-id.use-case';
import { CreateContractUseCase } from '@/modules/documents/application/use-cases/create-contract.use-case';
import { ListContractUseCase } from '@/modules/documents/application/use-cases/list-contract.use-case';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import { ValidateSupplierActivationUseCase } from '@/core/application/use-cases/supplier/validate-supplier-activation.use-case';
import { DownloadDocumentUseCase } from '../../../documents/application/use-cases/download-document.use-case';
import { DeleteContractUseCase } from '@/modules/documents/application/use-cases/delete-contract.use-case';
import { IContractRepository } from '@/modules/documents/domain/repositories/contract.repository.interface';
import { UpdateContractUseCase } from '@/modules/documents/application/use-cases/update-contract.use-case';
import { DownloadContractUseCase } from '@/modules/documents/application/use-cases/download-contract.use-case';
import { CreateContractsDto } from '@/modules/documents/infrastructure/dto/create-contract.dto';
import { EntityType } from '@/modules/documents/domain/enums/entity-type.enum';
import { ContractSignPatchDto, ContractUpdatePatchDto } from '@/modules/documents/infrastructure/dto/create-contract.dto';
import { ContractStatus, ContractType } from '@prisma/client';

describe('SupplierService', () => {
  let service: SupplierService;
  let createSupplierUseCase: CreateSupplierUseCase;
  let listSupplierByUuidUseCase: ListSupplierByUuidUseCase;
  let listSuppliersUseCase: ListSuppliersUseCase;
  let deleteSupplierUseCase: DeleteSupplierUseCase;
  let updateSupplierUseCase: UpdateSupplierUseCase;
  let prisma: PrismaService;
  let contractRepository: IContractRepository;

  // Mock supplier response data
  const mockSupplierData = {
    id: 'supplier-uuid',
    name: 'Test Supplier',
    cnpj: '********901234',
    tradeName: 'Test Trade Name',
    address: {
      street: 'Test Street',
      city: 'Test City',
      zipCode: '********',
      state: 'TS',
      number: undefined,
      complement: undefined,
      neighborhood: undefined,
    },
    status: SupplierStatus.ACTIVE,
    type: SupplierType.BANK,
    email: '<EMAIL>',
    createdAt: '2023-01-01T00:00:00.000Z',
    createdBy: 'user-uuid',
    updatedAt: '2023-01-01T00:00:00.000Z',
    updatedBy: 'user-uuid',
  };

  // Mock the Supplier entity
  const mockSupplier = {
    id: 'supplier-uuid',
    name: 'Test Supplier',
    document: '********901234',
    tradeName: 'Test Trade Name',
    address: {
      toJSON: () => ({
        street: 'Test Street',
        city: 'Test City',
        zipCode: '********',
        state: 'TS',
        number: undefined,
        complement: undefined,
        neighborhood: undefined,
      }),
    },
    status: SupplierStatus.ACTIVE,
    type: SupplierType.BANK,
    email: '<EMAIL>',
    createdAt: new Date('2023-01-01'),
    createdBy: 'user-uuid',
    updatedAt: new Date('2023-01-01'),
    updatedBy: 'user-uuid',
    toJSON: () => mockSupplierData,
  } as unknown as Supplier;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SupplierService,
        {
          provide: CreateSupplierUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: ListSupplierByUuidUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: ListSuppliersUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: DeleteSupplierUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: UpdateSupplierUseCase,
          useValue: {
            execute: jest.fn(),
          },
        },
        {
          provide: CreateDocumentUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: ListDocumentsUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: ListSupplierContactsUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: CreateSupplierContactUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: UpdateSupplierContactUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DeleteSupplierContactUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: GetSupplierByUserIdUsecase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: CreateContractUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: ListContractUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: PrismaService,
          useValue: {
            supplier: {
              findUnique: jest.fn(),
            },
          },
        },
        {
          provide: ValidateSupplierActivationUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DownloadDocumentUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DeleteContractUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: 'IContractRepository',
          useValue: {
            findByUuid: jest.fn(),
            createVersion: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: UpdateContractUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DownloadContractUseCase,
          useValue: { execute: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<SupplierService>(SupplierService);
    createSupplierUseCase = module.get<CreateSupplierUseCase>(
      CreateSupplierUseCase,
    );
    listSupplierByUuidUseCase = module.get<ListSupplierByUuidUseCase>(
      ListSupplierByUuidUseCase,
    );
    listSuppliersUseCase =
      module.get<ListSuppliersUseCase>(ListSuppliersUseCase);
    deleteSupplierUseCase = module.get<DeleteSupplierUseCase>(
      DeleteSupplierUseCase,
    );
    updateSupplierUseCase = module.get<UpdateSupplierUseCase>(
      UpdateSupplierUseCase,
    );
    prisma = module.get<PrismaService>(PrismaService);
    contractRepository = module.get<IContractRepository>('IContractRepository');
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSupplier', () => {
    it('should create a supplier and return it as DTO', async () => {
      const createDto: CreateSupplierDto = {
        name: 'Test Supplier',
        document: '********901234',
        tradeName: 'Test Trade Name',
        address: {
          street: 'Test Street',
          city: 'Test City',
          zipCode: '********',
          state: 'TS',
        },
        email: '<EMAIL>',
        type: SupplierType.BANK,
      };
      const userId = 'user-uuid';

      const executeSpy = jest
        .spyOn(createSupplierUseCase, 'execute')
        .mockResolvedValue(mockSupplier);

      const result = await service.createSupplier(createDto, userId);

      expect(executeSpy).toHaveBeenCalledWith(createDto, userId);
      expect(result).toEqual(mockSupplierData);
    });

    it('should create a supplier with GENERAL classification for OTHER type', async () => {
      const createDto: CreateSupplierDto = {
        name: 'Test Supplier',
        document: '********901234',
        tradeName: 'Test Trade Name',
        address: {
          street: 'Test Street',
          city: 'Test City',
          zipCode: '********',
          state: 'TS',
        },
        email: '<EMAIL>',
        type: SupplierType.OTHER,
      };
      const userId = 'user-uuid';

      const executeSpy = jest
        .spyOn(createSupplierUseCase, 'execute')
        .mockResolvedValue(mockSupplier);

      await service.createSupplier(createDto, userId);

      expect(executeSpy).toHaveBeenCalledWith(createDto, userId);
    });
  });

  describe('findByUserId', () => {
    it('should return a supplier by user id', async () => {
      const userId = 'user-uuid';

      const executeSpy = jest
        .spyOn(
          service['getSupplierByUserIdUseCase'],
          'execute',
        )
        .mockResolvedValue(mockSupplier);

      const result = await service.findByUserId(userId);

      expect(executeSpy).toHaveBeenCalledWith(userId);
      expect(result).toEqual(mockSupplierData);
    });

    it('should throw a not found exception if supplier is not found by user id', async () => {
      const userId = 'user-uuid';

      jest
        .spyOn(
          service['getSupplierByUserIdUseCase'],
          'execute',
        )
        .mockImplementation(() => {
          throw new Error('Supplier not found');
        });

      await expect(service.findByUserId(userId)).rejects.toThrow(
        'Supplier not found',
      );
    });
  });

  describe('listSupplierContracts', () => {
    it('should return a list of contracts with download URLs', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockContracts = [
        {
          uuid: 'contract-uuid',
          currentVersion: 1,
          versions: [{ versionId: 1 }],
        },
      ] as any[];

      jest
        .spyOn(service['listContractUseCase'], 'execute')
        .mockResolvedValue({ items: mockContracts, total: 1 });

      jest
        .spyOn(service['downloadContractUseCase'], 'execute')
        .mockResolvedValue({
          url: 'http://example.com/download',
          fileName: 'contract.pdf',
        });

      const result = await service.listSupplierContracts(supplierUuid);

      expect(result[0]).toHaveProperty('downloadUrl');
      expect(result[0].downloadUrl).toBe('http://example.com/download');
    });

    it('should return contracts without download URLs if they have no versions', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockContracts = [{ uuid: 'contract-uuid', versions: [] }] as any[];

      jest
        .spyOn(service['listContractUseCase'], 'execute')
        .mockResolvedValue({ items: mockContracts, total: 1 });

      const result = await service.listSupplierContracts(supplierUuid);

      expect(result[0]).not.toHaveProperty('downloadUrl');
    });

    it('should return contract without download URL if URL generation fails', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockContracts = [
        {
          uuid: 'contract-uuid',
          currentVersion: 1,
          versions: [{ versionId: 1 }],
        },
      ] as any[];

      jest
        .spyOn(service['listContractUseCase'], 'execute')
        .mockResolvedValue({ items: mockContracts, total: 1 });

      jest
        .spyOn(service['downloadContractUseCase'], 'execute')
        .mockImplementation(() => {
          throw new Error('URL generation failed');
        });

      const result = await service.listSupplierContracts(supplierUuid);

      expect(result[0]).not.toHaveProperty('downloadUrl');
    });

    it('should return an empty list if no contracts are found', async () => {
      const supplierUuid = 'supplier-uuid';

      jest
        .spyOn(service['listContractUseCase'], 'execute')
        .mockResolvedValue({ items: [], total: 0 });

      const result = await service.listSupplierContracts(supplierUuid);

      expect(result).toEqual([]);
    });
  });

  describe('listSupplierDocuments', () => {
    it('should return a list of documents with download URLs', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockDocuments = [
        {
          uuid: 'document-uuid',
          currentVersion: 1,
          versions: [{ versionId: 1 }],
        },
      ] as any[];

      jest
        .spyOn(service['listDocumentsUseCase'], 'execute')
        .mockResolvedValue({ items: mockDocuments, total: 1, limit: 10, offset: 0 });

      jest
        .spyOn(service['downloadDocumentUseCase'], 'execute')
        .mockResolvedValue({
          url: 'http://example.com/download',
          fileName: 'document.pdf',
        });

      const result = await service.listSupplierDocuments(supplierUuid);

      expect(result[0]).toHaveProperty('downloadUrl');
      expect(result[0].downloadUrl).toBe('http://example.com/download');
    });

    it('should return documents without download URLs if they have no versions', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockDocuments = [{ uuid: 'document-uuid', versions: [] }] as any[];

      jest.spyOn(service['listDocumentsUseCase'], 'execute').mockResolvedValue({
        items: mockDocuments,
        total: 1,
        limit: 10,
        offset: 0,
      });

      const result = await service.listSupplierDocuments(supplierUuid);

      expect(result[0]).not.toHaveProperty('downloadUrl');
    });

    it('should return document without download URL if URL generation fails', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockDocuments = [
        {
          uuid: 'document-uuid',
          currentVersion: 1,
          versions: [{ versionId: 1 }],
        },
      ] as any[];

      jest.spyOn(service['listDocumentsUseCase'], 'execute').mockResolvedValue({
        items: mockDocuments,
        total: 1,
        limit: 10,
        offset: 0,
      });

      jest
        .spyOn(service['downloadDocumentUseCase'], 'execute')
        .mockImplementation(() => {
          throw new Error('URL generation failed');
        });

      const result = await service.listSupplierDocuments(supplierUuid);

      expect(result[0]).not.toHaveProperty('downloadUrl');
    });

    it('should return an empty list if no documents are found', async () => {
      const supplierUuid = 'supplier-uuid';

      jest.spyOn(service['listDocumentsUseCase'], 'execute').mockResolvedValue({
        items: [],
        total: 0,
        limit: 10,
        offset: 0,
      });

      const result = await service.listSupplierDocuments(supplierUuid);

      expect(result).toEqual([]);
    });
  });

  describe('listSupplierContacts', () => {
    it('should return a list of contacts', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockContacts = [
        {
          id: 'contact-uuid',
          contact: '<EMAIL>',
          type: 'email',
          area: 'Sales',
          responsible: 'John Doe',
        },
      ] as any[];

      jest
        .spyOn(service['listSupplierContactsUseCase'], 'execute')
        .mockResolvedValue(mockContacts);

      const result = await service.listSupplierContacts(supplierUuid);

      expect(result.contacts).toHaveLength(1);
      expect(result.contacts[0].contact).toBe('<EMAIL>');
    });

    it('should return an empty list if no contacts are found', async () => {
      const supplierUuid = 'supplier-uuid';

      jest
        .spyOn(service['listSupplierContactsUseCase'], 'execute')
        .mockResolvedValue([]);

      const result = await service.listSupplierContacts(supplierUuid);

      expect(result.contacts).toEqual([]);
    });
  });

  describe('getSupplierTypes', () => {
    it('should return a list of supplier types', () => {
      const result = service.getSupplierTypes();
      expect(result).toEqual(Object.values(SupplierType));
    });
  });

  describe('listSupplierByUuid', () => {
    it('should return a supplier by uuid', async () => {
      const uuid = 'supplier-uuid';

      const executeSpy = jest
        .spyOn(listSupplierByUuidUseCase, 'execute')
        .mockResolvedValue(mockSupplier);

      const result = await service.listSupplierByUuid(uuid);

      expect(executeSpy).toHaveBeenCalledWith(uuid);
      expect(result).toEqual(mockSupplierData);
    });
  });

  describe('listSuppliers', () => {
    it('should return a paginated list of suppliers', async () => {
      const params = { limit: 10, offset: 0, name: 'Test' };
      const mockResult = {
        items: [mockSupplier],
        total: 1,
      };

      const executeSpy = jest
        .spyOn(listSuppliersUseCase, 'execute')
        .mockResolvedValue(mockResult);

      const result = await service.listSuppliers(params);

      expect(executeSpy).toHaveBeenCalledWith({
        limit: 10,
        offset: 0,
        name: 'Test',
      });
      expect(result).toEqual({
        items: [mockSupplierData],
        total: 1,
        limit: 10,
        offset: 0,
      });
    });
  });

  describe('deleteSupplier', () => {
    it('should delete a supplier', async () => {
      const uuid = 'supplier-uuid';

      const executeSpy = jest
        .spyOn(deleteSupplierUseCase, 'execute')
        .mockResolvedValue(undefined);

      await service.deleteSupplier(uuid);

      expect(executeSpy).toHaveBeenCalledWith({ uuid });
    });
  });

  describe('updateSupplier', () => {
    it('should update a supplier and return it as DTO', async () => {
      const uuid = 'supplier-uuid';
      const updateDto: UpdateSupplierDto = {
        name: 'Updated Supplier',
        tradeName: 'Updated Trade Name',
        address: {
          street: 'Updated Street',
          city: 'Updated City',
          zipCode: '********',
          state: 'US',
        },
        email: '<EMAIL>',
        type: SupplierType.BANK,
      };
      const userId = 'user-uuid';

      // Updated supplier data
      const updatedSupplierData = {
        id: 'supplier-uuid',
        name: 'Updated Supplier',
        cnpj: '********901234',
        tradeName: 'Updated Trade Name',
        address: {
          street: 'Updated Street',
          city: 'Updated City',
          zipCode: '********',
          state: 'US',
          number: undefined,
          complement: undefined,
          neighborhood: undefined,
        },
        status: SupplierStatus.ACTIVE,
        type: SupplierType.BANK,
        email: '<EMAIL>',
        createdAt: '2023-01-01T00:00:00.000Z',
        createdBy: 'user-uuid',
        updatedAt: '2023-01-02T00:00:00.000Z',
        updatedBy: userId,
      };

      // Create a separate mock for the updated supplier
      const updatedMockSupplier = {
        id: 'supplier-uuid',
        name: 'Updated Supplier',
        document: '********901234',
        tradeName: 'Updated Trade Name',
        address: {
          toJSON: () => ({
            street: 'Updated Street',
            city: 'Updated City',
            zipCode: '********',
            state: 'US',
            number: undefined,
            complement: undefined,
            neighborhood: undefined,
          }),
        },
        status: SupplierStatus.ACTIVE,
        type: SupplierType.BANK,
        email: '<EMAIL>',
        createdAt: new Date('2023-01-01'),
        createdBy: 'user-uuid',
        updatedAt: new Date('2023-01-02'),
        updatedBy: userId,
        toJSON: () => updatedSupplierData,
      } as unknown as Supplier;

      const executeSpy = jest
        .spyOn(updateSupplierUseCase, 'execute')
        .mockResolvedValue(updatedMockSupplier);

      const result = await service.updateSupplier(uuid, updateDto, userId);

      expect(executeSpy).toHaveBeenCalledWith(uuid, updateDto, userId);
      expect(result).toEqual(updatedSupplierData);
    });
  });

  describe('createSupplierContract', () => {
    it('should create a supplier contract successfully', async () => {
      const supplierUuid = 'supplier-uuid';
      const userId = 'user-uuid';
      const files = [] as any[];
      const createContractsDto: CreateContractsDto = {
        contracts: [
          {
            contractIdentifier: 'contract.pdf',
            entityType: EntityType.SUPPLIER,
            entityActualUuid: supplierUuid,
          },
        ],
      };
      const mockCreatedContracts = [{ uuid: 'contract-uuid' }] as any[];

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue({} as any);
      jest
        .spyOn(service['createContractUseCase'], 'execute')
        .mockResolvedValue(mockCreatedContracts);
      jest
        .spyOn(service['validateSupplierActivationUseCase'], 'execute')
        .mockResolvedValue(true);

      const result = await service.createSupplierContract(
        supplierUuid,
        files,
        userId,
        createContractsDto,
      );

      expect(result).toEqual(mockCreatedContracts);
      expect(prisma.supplier.findUnique).toHaveBeenCalledWith({
        where: { id: supplierUuid },
      });
      expect(service['createContractUseCase'].execute).toHaveBeenCalled();
      expect(
        service['validateSupplierActivationUseCase'].execute,
      ).toHaveBeenCalled();
    });

    it('should throw NotFoundException if supplier does not exist', async () => {
      const supplierUuid = 'non-existent-uuid';
      const userId = 'user-uuid';
      const files = [] as any[];
      const createContractsDto: CreateContractsDto = { contracts: [] };

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue(null);

      await expect(
        service.createSupplierContract(
          supplierUuid,
          files,
          userId,
          createContractsDto,
        ),
      ).rejects.toThrow(
        `Fornecedor com UUID ${supplierUuid} não encontrado.`,
      );
    });

    it('should still create contract if supplier activation fails', async () => {
      const supplierUuid = 'supplier-uuid';
      const userId = 'user-uuid';
      const files = [] as any[];
      const createContractsDto: CreateContractsDto = {
        contracts: [
          {
            contractIdentifier: 'contract.pdf',
            entityType: EntityType.SUPPLIER,
            entityActualUuid: supplierUuid,
          },
        ],
      };
      const mockCreatedContracts = [{ uuid: 'contract-uuid' }] as any[];

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue({} as any);
      jest
        .spyOn(service['createContractUseCase'], 'execute')
        .mockResolvedValue(mockCreatedContracts);
      jest
        .spyOn(service['validateSupplierActivationUseCase'], 'execute')
        .mockRejectedValue(new Error('Activation failed'));

      const result = await service.createSupplierContract(
        supplierUuid,
        files,
        userId,
        createContractsDto,
      );

      expect(result).toEqual(mockCreatedContracts);
    });
  });

  describe('updateSupplierContract', () => {
    it('should update a contract successfully when signed is true', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'contract-uuid';
      const patch: ContractUpdatePatchDto = { signed: true };
      const mockContract = {
        uuid: contractUuid,
        entityUuid: supplierUuid,
        createdBy: 'user-uuid',
        currentVersion: 1,
        versions: [{ versionId: 1, filePath: 'path/to/file' }],
      } as any;
      const mockUpdatedContract = { ...mockContract, status: ContractStatus.APPROVED };

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);
      jest.spyOn(contractRepository, 'update').mockResolvedValue(mockUpdatedContract);

      const result = await service.updateSupplierContract(
        supplierUuid,
        contractUuid,
        patch,
      );

      expect(contractRepository.findByUuid).toHaveBeenCalledWith(contractUuid);
      expect(contractRepository.createVersion).toHaveBeenCalledWith(
        contractUuid,
        expect.objectContaining({ signed: true }),
      );
      expect(contractRepository.update).toHaveBeenCalledWith(contractUuid, {
        status: ContractStatus.APPROVED,
      });
      expect(result).toEqual(mockUpdatedContract);
    });

    it('should update a contract successfully when signed is false', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'contract-uuid';
      const patch: ContractUpdatePatchDto = { signed: false };
      const mockContract = {
        uuid: contractUuid,
        entityUuid: supplierUuid,
        createdBy: 'user-uuid',
        currentVersion: 1,
        versions: [{ versionId: 1, filePath: 'path/to/file' }],
      } as any;
      const mockUpdatedContract = { ...mockContract, status: ContractStatus.REJECTED };

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);
      jest.spyOn(contractRepository, 'update').mockResolvedValue(mockUpdatedContract);

      const result = await service.updateSupplierContract(
        supplierUuid,
        contractUuid,
        patch,
      );

      expect(contractRepository.update).toHaveBeenCalledWith(contractUuid, {
        status: ContractStatus.REJECTED,
      });
      expect(result).toEqual(mockUpdatedContract);
    });

    it('should throw NotFoundException if contract is not found', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'non-existent-contract';
      const patch: ContractUpdatePatchDto = { signed: true };

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(null);

      await expect(
        service.updateSupplierContract(supplierUuid, contractUuid, patch),
      ).rejects.toThrow('Contrato não encontrado para este fornecedor');
    });

    it('should throw NotFoundException if contract does not belong to supplier', async () => {
      const supplierUuid = 'supplier-uuid';
      const anotherSupplierUuid = 'another-supplier-uuid';
      const contractUuid = 'contract-uuid';
      const patch: ContractUpdatePatchDto = { signed: true };
      const mockContract = {
        uuid: contractUuid,
        entityUuid: anotherSupplierUuid,
      } as any;

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);

      await expect(
        service.updateSupplierContract(supplierUuid, contractUuid, patch),
      ).rejects.toThrow('Contrato não encontrado para este fornecedor');
    });

    it('should update contract with all fields', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'contract-uuid';
      const patch: ContractUpdatePatchDto = {
        contractType: ContractType.CERT_PLATFORM,
        signed: true,
        startDate: '2024-01-01',
        expirationDate: '2025-12-31',
        observations: 'Updated observations',
      };
      const mockContract = {
        uuid: contractUuid,
        entityUuid: supplierUuid,
        createdBy: 'user-uuid',
        currentVersion: 1,
        versions: [{ versionId: 1, filePath: 'path/to/file' }],
      } as any;
      const mockUpdatedContract = { ...mockContract, contractType: ContractType.CERT_PLATFORM, status: ContractStatus.APPROVED };

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);
      jest.spyOn(contractRepository, 'update').mockResolvedValue(mockUpdatedContract);

      const result = await service.updateSupplierContract(
        supplierUuid,
        contractUuid,
        patch,
      );

      expect(contractRepository.findByUuid).toHaveBeenCalledWith(contractUuid);
      expect(contractRepository.createVersion).toHaveBeenCalledWith(
        contractUuid,
        expect.objectContaining({
          signed: true,
          startDate: new Date('2024-01-01'),
          expirationDate: new Date('2025-12-31'),
          observations: 'Updated observations',
        }),
      );
      expect(contractRepository.update).toHaveBeenCalledWith(contractUuid, {
        contractType: ContractType.CERT_PLATFORM,
        status: ContractStatus.APPROVED,
      });
      expect(result).toEqual(mockUpdatedContract);
    });
  });

  describe('createSupplierContacts', () => {
    it('should create contacts and activate supplier successfully', async () => {
      const supplierUuid = 'supplier-uuid';
      const contactsData = [
        {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Sales',
          responsible: 'John Doe',
        },
        {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Support',
          responsible: 'Jane Doe',
        },
      ];
      const mockCreatedContact = {
        id: 'contact-uuid',
        contact: '<EMAIL>',
        type: 'email',
        area: 'Sales',
        responsible: 'John Doe',
      };

      jest
        .spyOn(service['createSupplierContactUseCase'], 'execute')
        .mockResolvedValue(mockCreatedContact as any);
      jest
        .spyOn(service['validateSupplierActivationUseCase'], 'execute')
        .mockResolvedValue(true);

      const result = await service.createSupplierContacts(
        supplierUuid,
        contactsData,
      );

      expect(
        service['createSupplierContactUseCase'].execute,
      ).toHaveBeenCalledTimes(2);
      expect(
        service['validateSupplierActivationUseCase'].execute,
      ).toHaveBeenCalledWith(supplierUuid, 'system');
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        contact: mockCreatedContact.contact,
        type: mockCreatedContact.type,
        area: mockCreatedContact.area,
        responsible: mockCreatedContact.responsible,
      });
    });

    it('should still create contacts if supplier activation fails', async () => {
      const supplierUuid = 'supplier-uuid';
      const contactsData = [
        {
          contact: '<EMAIL>',
          type: 'email',
          area: 'Sales',
          responsible: 'John Doe',
        },
      ];
      const mockCreatedContact = {
        id: 'contact-uuid',
        contact: '<EMAIL>',
        type: 'email',
        area: 'Sales',
        responsible: 'John Doe',
      };

      jest
        .spyOn(service['createSupplierContactUseCase'], 'execute')
        .mockResolvedValue(mockCreatedContact as any);
      jest
        .spyOn(service['validateSupplierActivationUseCase'], 'execute')
        .mockRejectedValue(new Error('Activation failed'));

      const result = await service.createSupplierContacts(
        supplierUuid,
        contactsData,
      );

      expect(
        service['createSupplierContactUseCase'].execute,
      ).toHaveBeenCalledTimes(1);
      expect(result).toHaveLength(1);
    });

    it('should handle empty contacts array and still attempt activation', async () => {
      const supplierUuid = 'supplier-uuid';
      const contactsData = [];

      jest
        .spyOn(service['validateSupplierActivationUseCase'], 'execute')
        .mockResolvedValue(true);

      const result = await service.createSupplierContacts(
        supplierUuid,
        contactsData,
      );

      expect(
        service['createSupplierContactUseCase'].execute,
      ).not.toHaveBeenCalled();
      expect(
        service['validateSupplierActivationUseCase'].execute,
      ).toHaveBeenCalledWith(supplierUuid, 'system');
      expect(result).toHaveLength(0);
    });
  });

  describe('uploadSupplierDocuments', () => {
    it('should upload documents and activate supplier successfully', async () => {
      const supplierUuid = 'supplier-uuid';
      const userId = 'user-uuid';
      const files = [{ originalname: 'test.pdf' }] as Express.Multer.File[];
      const documentsMetadata = JSON.stringify([
        {
          responsible: 'John Doe',
          department: 'Legal',
          description: 'Contract',
          expirationDate: '2025-12-31',
        },
      ]);
      const mockDocument = { uuid: 'document-uuid' } as any;

      jest
        .spyOn(service['createDocumentUseCase'], 'execute')
        .mockResolvedValue(mockDocument);
      jest
        .spyOn(service['validateSupplierActivationUseCase'], 'execute')
        .mockResolvedValue(true);

      const result = await service.uploadSupplierDocuments(
        supplierUuid,
        files,
        documentsMetadata,
        userId,
      );

      expect(result).toEqual([mockDocument]);
      expect(service['createDocumentUseCase'].execute).toHaveBeenCalledTimes(1);
      expect(
        service['validateSupplierActivationUseCase'].execute,
      ).toHaveBeenCalledWith(supplierUuid, userId);
    });

    it('should throw an error for invalid JSON metadata', async () => {
      const supplierUuid = 'supplier-uuid';
      const userId = 'user-uuid';
      const files = [] as Express.Multer.File[];
      const invalidJson = 'not a json';

      await expect(
        service.uploadSupplierDocuments(supplierUuid, files, invalidJson, userId),
      ).rejects.toThrow('O campo documentsMetadata não é uma string JSON válida.');
    });

    it('should throw an error if files and metadata count mismatch', async () => {
      const supplierUuid = 'supplier-uuid';
      const userId = 'user-uuid';
      const files = [{ originalname: 'test.pdf' }] as Express.Multer.File[];
      const documentsMetadata = JSON.stringify([]); // Mismatch

      await expect(
        service.uploadSupplierDocuments(
          supplierUuid,
          files,
          documentsMetadata,
          userId,
        ),
      ).rejects.toThrow(
        'O número de metadados de documento não corresponde ao número de arquivos enviados.',
      );
    });

    it('should still upload documents if supplier activation fails', async () => {
      const supplierUuid = 'supplier-uuid';
      const userId = 'user-uuid';
      const files = [{ originalname: 'test.pdf' }] as Express.Multer.File[];
      const documentsMetadata = JSON.stringify([
        {
          responsible: 'John Doe',
          department: 'Legal',
          description: 'Contract',
          expirationDate: '2025-12-31',
        },
      ]);
      const mockDocument = { uuid: 'document-uuid' } as any;

      jest
        .spyOn(service['createDocumentUseCase'], 'execute')
        .mockResolvedValue(mockDocument);
      jest
        .spyOn(service['validateSupplierActivationUseCase'], 'execute')
        .mockRejectedValue(new Error('Activation failed'));

      const result = await service.uploadSupplierDocuments(
        supplierUuid,
        files,
        documentsMetadata,
        userId,
      );

      expect(result).toEqual([mockDocument]);
    });
  });

  describe('deleteSupplierContract', () => {
    it('should delete a contract successfully', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'contract-uuid';
      const mockContract = {
        uuid: contractUuid,
        entityUuid: supplierUuid,
      } as any;

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);
      jest
        .spyOn(service['deleteContractUseCase'], 'execute')
        .mockResolvedValue(undefined);

      await service.deleteSupplierContract(supplierUuid, contractUuid);

      expect(contractRepository.findByUuid).toHaveBeenCalledWith(contractUuid);
      expect(service['deleteContractUseCase'].execute).toHaveBeenCalledWith(
        contractUuid,
      );
    });

    it('should throw NotFoundException if contract is not found', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'non-existent-contract';

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(null);

      await expect(
        service.deleteSupplierContract(supplierUuid, contractUuid),
      ).rejects.toThrow('Contrato não encontrado para este fornecedor');
    });

    it('should throw NotFoundException if contract does not belong to supplier', async () => {
      const supplierUuid = 'supplier-uuid';
      const anotherSupplierUuid = 'another-supplier-uuid';
      const contractUuid = 'contract-uuid';
      const mockContract = {
        uuid: contractUuid,
        entityUuid: anotherSupplierUuid,
      } as any;

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);

      await expect(
        service.deleteSupplierContract(supplierUuid, contractUuid),
      ).rejects.toThrow('Contrato não encontrado para este fornecedor');
    });
  });
});
